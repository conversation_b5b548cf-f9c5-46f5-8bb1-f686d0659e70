package server

import (
	"github.com/gin-gonic/gin"

	"realmaster-video-backend/internal/domain/advertiser"
	"realmaster-video-backend/internal/domain/category"
	"realmaster-video-backend/internal/domain/fileupload"
	"realmaster-video-backend/internal/domain/main_property"
	"realmaster-video-backend/internal/domain/video"
)

// RegisterCategoryRoutes 注册分类相关的路由
func RegisterCategoryRoutes(router gin.IRouter, handler *category.Handler) {
	// 分类管理路由
	categories := router.Group("/categories")
	{
		categories.GET("/", handler.ListCategories)
		categories.POST("/", handler.CreateCategory)
		categories.PUT("/:id", handler.UpdateCategory)
		categories.DELETE("/:id", handler.DeleteCategory)
	}
}

// RegisterAdvertiserRoutes 注册广告主相关的路由
func RegisterAdvertiserRoutes(router gin.IRouter, handler *advertiser.Handler) {
	advertisers := router.Group("/advertisers")
	{
		advertisers.GET("", handler.List)
		advertisers.GET("/:id", handler.GetByID)
		advertisers.POST("", handler.Create)
		advertisers.PATCH("/:id", handler.Update)
		advertisers.DELETE("/:id", handler.Delete)
	}
}

// RegisterMainPropertyRoutes 注册房源相关的路由
func RegisterMainPropertyRoutes(router gin.IRouter, propertyHandler *main_property.Handler) {
	api := router.Group("/properties")
	{
		api.POST("/search", propertyHandler.SearchByKeyword)
		api.POST("/batch-get", propertyHandler.GetByIDs)
		api.GET("/:id", propertyHandler.GetByID)
	}
}

// RegisterVideoRoutes registers routes for the video domain.
func RegisterVideoRoutes(router gin.IRouter, handler *video.Handler) {
	// 管理后台 API
	adminVideoRoutes := router.Group("/videos")
	{
		// Stats endpoint should be before the one with path parameter (`/:id`)
		adminVideoRoutes.GET("/stats", handler.GetVideoStats)
		adminVideoRoutes.GET("", handler.FindVideos)
		adminVideoRoutes.GET("/", handler.FindVideos)
		adminVideoRoutes.POST("/", handler.CreateDraft)
		adminVideoRoutes.DELETE("/:id", handler.DeleteVideo)
		adminVideoRoutes.PATCH("/:id", handler.UpdateVideo)
		adminVideoRoutes.GET("/:id", handler.GetByID)
		adminVideoRoutes.POST("/:id/publish", handler.PublishVideo)
		adminVideoRoutes.PATCH("/:id/stats", handler.UpdateVideoStats)

		// 单独的缩略图上传路由（用于大文件分块上传场景）
		adminVideoRoutes.POST("/upload-thumbnail", handler.UploadThumbnail)
	}

	// Route for serving temporary content securely
	adminVideoRoutes.GET("/temp-content/:filename", handler.ServeTempContent)

	// 分块上传路由
	chunkedUpload := adminVideoRoutes.Group("/chunked-upload")
	{
		chunkedUpload.POST("/initiate", handler.InitiateChunkedUpload)
		chunkedUpload.POST("/:uploadId/chunk/:chunkNumber", handler.UploadChunk)
		chunkedUpload.POST("/complete", handler.CompleteChunkedUpload)
	}
}

// RegisterFileUploadRoutes registers routes for the internal file upload service.
func RegisterFileUploadRoutes(router *gin.Engine, fileUploadHandler *fileupload.FileUploadHandler) {
	// This group is for internal services like the video-worker
	internalAPI := router.Group("/upload")
	{
		internalAPI.POST("/video/:videoID/:fileName", fileUploadHandler.HandleUpload)
	}
}
