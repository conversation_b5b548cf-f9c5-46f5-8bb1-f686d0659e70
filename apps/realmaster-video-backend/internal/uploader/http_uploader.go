package uploader

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"path/filepath"

	"realmaster-video-backend/internal/platform/logger"

)

// HTTPUploader 通过标准的 HTTP POST 请求上传文件
type HTTPUploader struct {
	BaseURL    string
	HTTPClient *http.Client
}

// NewHTTPUploader 创建一个新的 HTTPUploader 实例
func NewHTTPUploader(baseURL string) *HTTPUploader {
	return &HTTPUploader{
		BaseURL:    baseURL,
		HTTPClient: &http.Client{},
	}
}

// UploadDirectory 实现了 Uploader 接口
func (u *HTTPUploader) UploadDirectory(ctx context.Context, sourcePath string, targetSubDir string) ([]UploadResult, error) {
	results := []UploadResult{}

	// filepath.Walk 会递归地遍历目录
	err := filepath.Walk(sourcePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		// 跳过目录本身
		if info.IsDir() {
			return nil
		}

		// 为每个文件执行上传
		result, uploadErr := u.uploadFile(ctx, path, targetSubDir)
		if uploadErr != nil {
			// 让 Walk 失败
			return fmt.Errorf("文件 '%s' 上传失败: %w", path, uploadErr)
		}
		results = append(results, *result)
		return nil
	})

	if err != nil {
		return nil, err
	}

	return results, nil
}

// UploadFile 实现了 Uploader 接口
func (u *HTTPUploader) UploadFile(ctx context.Context, sourceFilePath string, targetSubDir string) (*UploadResult, error) {
	return u.uploadFile(ctx, sourceFilePath, targetSubDir)
}

// uploadFile 是上传单个文件的辅助函数
func (u *HTTPUploader) uploadFile(ctx context.Context, localFilePath string, targetSubDir string) (*UploadResult, error) {
	file, err := os.Open(localFilePath)
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	// 准备请求体 (multipart/form-data)
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, err := writer.CreateFormFile("file", filepath.Base(localFilePath))
	if err != nil {
		return nil, fmt.Errorf("创建 form file 失败: %w", err)
	}
	if _, err := io.Copy(part, file); err != nil {
		return nil, fmt.Errorf("向 form-data 写入文件内容失败: %w", err)
	}
	writer.Close() // 非常重要，这会写入结尾的 boundary

	// 构建目标 URL，确保它与服务器路由匹配
	// 最终格式: {BaseURL}/upload/video/{targetSubDir}/{fileName}
	uploadURL, err := url.Parse(u.BaseURL)
	if err != nil {
		return nil, fmt.Errorf("解析基础URL失败: %w", err)
	}

	// 我们手动拼接路径，以确保它总是正确的格式
	uploadURL.Path = filepath.Join(uploadURL.Path, "upload", "video", targetSubDir, filepath.Base(localFilePath))
	targetURL := uploadURL.String()

	// 创建 HTTP 请求
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, targetURL, body)
	if err != nil {
		return nil, fmt.Errorf("创建 HTTP 请求失败: %w", err)
	}
	req.Header.Set("Content-Type", writer.FormDataContentType())

	logger.Log.Info("开始上传文件...",
		logger.String("from", localFilePath),
		logger.String("to", targetURL),
	)

	// 执行请求
	resp, err := u.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("执行 HTTP 请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		respBody, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("上传失败，服务器返回非成功状态码 %d, body: %s", resp.StatusCode, string(respBody))
	}

	// 构造一个纯粹的、相对的路径，供数据库存储
	// 格式: media/video-id/filename.ext
	relativePath := filepath.ToSlash(filepath.Join("media", targetSubDir, filepath.Base(localFilePath)))

	result := &UploadResult{
		FileName: filepath.Base(localFilePath),
		URL:      relativePath,   // 返回相对路径
		Path:     uploadURL.Path, // 原始的完整路径，保留以备调试
	}

	logger.Log.Info("文件上传成功", logger.String("relativePath", result.URL))

	return result, nil
}
