package uploader

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"realmaster-video-backend/internal/platform/logger"

	levelStore "github.com/real-rm/golevelstore"
	"github.com/real-rm/gomongo"
	"github.com/real-rm/goupload"
)

// GoUploadService 封装 goupload 功能
type GoUploadService struct {
	// 草稿阶段统计更新器
	videoDraftStatsUpdater     goupload.StatsUpdater
	thumbnailDraftStatsUpdater goupload.StatsUpdater
	// 最终阶段统计更新器
	videoFinalStatsUpdater     goupload.StatsUpdater
	thumbnailFinalStatsUpdater goupload.StatsUpdater
	site                       string
}

// NewGoUploadService 创建新的 goupload 服务实例
func NewGoUploadService(site string) (*GoUploadService, error) {
	// 获取统计集合
	statsColl := gomongo.Coll("realmaster_video", "upload_stats")

	// 创建草稿阶段统计更新器
	videoDraftStatsUpdater, err := goupload.NewStatsUpdater(site, "video_draft", statsColl)
	if err != nil {
		return nil, fmt.Errorf("创建视频草稿统计更新器失败: %w", err)
	}

	thumbnailDraftStatsUpdater, err := goupload.NewStatsUpdater(site, "thumbnail_draft", statsColl)
	if err != nil {
		return nil, fmt.Errorf("创建缩略图草稿统计更新器失败: %w", err)
	}

	// 创建最终阶段统计更新器
	videoFinalStatsUpdater, err := goupload.NewStatsUpdater(site, "video_final", statsColl)
	if err != nil {
		return nil, fmt.Errorf("创建视频最终统计更新器失败: %w", err)
	}

	thumbnailFinalStatsUpdater, err := goupload.NewStatsUpdater(site, "thumbnail_final", statsColl)
	if err != nil {
		return nil, fmt.Errorf("创建缩略图最终统计更新器失败: %w", err)
	}

	return &GoUploadService{
		videoDraftStatsUpdater:     videoDraftStatsUpdater,
		thumbnailDraftStatsUpdater: thumbnailDraftStatsUpdater,
		videoFinalStatsUpdater:     videoFinalStatsUpdater,
		thumbnailFinalStatsUpdater: thumbnailFinalStatsUpdater,
		site:                       site,
	}, nil
}

// UploadVideo 上传视频文件
func (s *GoUploadService) UploadVideo(ctx context.Context, userID string, reader io.Reader, filename string, size int64) (*goupload.UploadResult, error) {
	logger.Log.Info("开始上传视频文件",
		logger.String("userID", userID),
		logger.String("filename", filename),
		logger.Int64("size", size),
	)

	result, err := goupload.Upload(
		ctx,
		s.videoDraftStatsUpdater,
		s.site,
		"video_draft",
		userID,
		reader,
		filename,
		size,
	)

	if err != nil {
		logger.Log.Error("视频上传失败",
			logger.String("userID", userID),
			logger.String("filename", filename),
			logger.Error(err),
		)
		return nil, s.handleUploadError(err)
	}

	logger.Log.Info("视频上传成功",
		logger.String("userID", userID),
		logger.String("filename", filename),
		logger.String("path", result.Path),
	)

	return result, nil
}

// UploadThumbnail 上传缩略图文件
func (s *GoUploadService) UploadThumbnail(ctx context.Context, userID string, reader io.Reader, filename string, size int64) (*goupload.UploadResult, error) {
	logger.Log.Info("开始上传缩略图文件",
		logger.String("userID", userID),
		logger.String("filename", filename),
		logger.Int64("size", size),
	)

	result, err := goupload.Upload(
		ctx,
		s.thumbnailDraftStatsUpdater,
		s.site,
		"thumbnail_draft",
		userID,
		reader,
		filename,
		size,
	)

	if err != nil {
		logger.Log.Error("缩略图上传失败",
			logger.String("userID", userID),
			logger.String("filename", filename),
			logger.Error(err),
		)
		return nil, s.handleUploadError(err)
	}

	logger.Log.Info("缩略图上传成功",
		logger.String("userID", userID),
		logger.String("filename", filename),
		logger.String("path", result.Path),
	)

	return result, nil
}

// InitiateChunkedVideoUpload 初始化分块视频上传
func (s *GoUploadService) InitiateChunkedVideoUpload(ctx context.Context, userID, filename string, totalSize int64) (string, error) {
	logger.Log.Info("初始化分块视频上传",
		logger.String("userID", userID),
		logger.String("filename", filename),
		logger.Int64("totalSize", totalSize),
	)

	provider := &goupload.LevelStoreProvider{}
	uploadID, err := goupload.InitiateChunkedUpload(
		ctx,
		provider,
		s.site,
		"video_draft",
		userID,
		filename,
		totalSize,
	)

	if err != nil {
		logger.Log.Error("初始化分块上传失败",
			logger.String("userID", userID),
			logger.String("filename", filename),
			logger.Error(err),
		)
		return "", fmt.Errorf("初始化分块上传失败: %w", err)
	}

	logger.Log.Info("分块上传初始化成功",
		logger.String("userID", userID),
		logger.String("uploadID", uploadID),
	)

	return uploadID, nil
}

// UploadChunk 上传文件分块
func (s *GoUploadService) UploadChunk(ctx context.Context, uploadID string, chunkNumber int, chunkReader io.Reader) error {
	logger.Log.Debug("上传文件分块",
		logger.String("uploadID", uploadID),
		logger.Int("chunkNumber", chunkNumber),
	)

	err := goupload.UploadChunk(ctx, uploadID, chunkNumber, chunkReader)
	if err != nil {
		logger.Log.Error("分块上传失败",
			logger.String("uploadID", uploadID),
			logger.Int("chunkNumber", chunkNumber),
			logger.Error(err),
		)
		return fmt.Errorf("分块上传失败: %w", err)
	}

	return nil
}

// CompleteChunkedVideoUpload 完成分块视频上传
func (s *GoUploadService) CompleteChunkedVideoUpload(ctx context.Context, uploadID string, expectedChunks int) (*goupload.UploadResult, error) {
	logger.Log.Info("完成分块视频上传",
		logger.String("uploadID", uploadID),
		logger.Int("expectedChunks", expectedChunks),
	)

	// S3 provider map (如果需要的话)
	s3ProviderMap := make(map[string]levelStore.S3ProviderConfig) // 根据实际需要配置

	result, err := goupload.CompleteChunkedUpload(
		ctx,
		s.videoDraftStatsUpdater,
		uploadID,
		expectedChunks,
		s3ProviderMap,
	)

	if err != nil {
		logger.Log.Error("完成分块上传失败",
			logger.String("uploadID", uploadID),
			logger.Error(err),
		)
		return nil, fmt.Errorf("完成分块上传失败: %w", err)
	}

	logger.Log.Info("分块上传完成",
		logger.String("uploadID", uploadID),
		logger.String("path", result.Path),
	)

	return result, nil
}

// DeleteFile 删除文件
func (s *GoUploadService) DeleteFile(ctx context.Context, entryName, relativePath string) error {
	logger.Log.Info("删除文件",
		logger.String("entryName", entryName),
		logger.String("relativePath", relativePath),
	)

	var statsUpdater goupload.StatsUpdater
	switch entryName {
	case "video_draft":
		statsUpdater = s.videoDraftStatsUpdater
	case "thumbnail_draft":
		statsUpdater = s.thumbnailDraftStatsUpdater
	case "video_final":
		statsUpdater = s.videoFinalStatsUpdater
	case "thumbnail_final":
		statsUpdater = s.thumbnailFinalStatsUpdater
	default:
		return fmt.Errorf("不支持的文件类型: %s", entryName)
	}

	result, err := goupload.Delete(ctx, statsUpdater, s.site, entryName, relativePath)
	if err != nil {
		logger.Log.Error("删除文件失败",
			logger.String("entryName", entryName),
			logger.String("relativePath", relativePath),
			logger.Error(err),
		)
		return fmt.Errorf("删除文件失败: %w", err)
	}

	if result.IsPartialDelete {
		logger.Log.Warn("文件部分删除",
			logger.String("relativePath", relativePath),
			logger.Int("deletedCount", len(result.DeletedPaths)),
			logger.Int("failedCount", len(result.FailedPaths)),
		)
	} else {
		logger.Log.Info("文件删除成功",
			logger.String("relativePath", relativePath),
			logger.Int("deletedCount", len(result.DeletedPaths)),
		)
	}

	return nil
}

// handleUploadError 处理上传错误，提供更友好的错误信息
func (s *GoUploadService) handleUploadError(err error) error {
	errMsg := err.Error()

	// 验证错误 - 客户端错误
	if strings.Contains(errMsg, "filename") ||
		strings.Contains(errMsg, "exceeds limit") ||
		strings.Contains(errMsg, "cannot be zero") {
		return fmt.Errorf("文件验证失败: %w", err)
	}

	// 写入错误 - 服务器错误
	if strings.Contains(errMsg, "write to") ||
		strings.Contains(errMsg, "failed to write") {
		return fmt.Errorf("文件写入失败: %w", err)
	}

	// 回滚错误 - 严重服务器错误
	if strings.Contains(errMsg, "rollback") {
		return fmt.Errorf("文件上传回滚失败: %w", err)
	}

	return err
}

// UploadDirectory 上传整个目录到最终位置（供Worker使用）
func (s *GoUploadService) UploadDirectory(ctx context.Context, entryName, userID, directoryName, localDirPath string) (*goupload.DirectoryUploadResult, error) {
	var statsUpdater goupload.StatsUpdater
	switch entryName {
	case "video_final":
		statsUpdater = s.videoFinalStatsUpdater
	case "thumbnail_final":
		statsUpdater = s.thumbnailFinalStatsUpdater
	default:
		return nil, fmt.Errorf("不支持的目录上传类型: %s", entryName)
	}

	// 构建目录上传请求
	request := &goupload.DirectoryUploadRequest{
		Site:          s.site,
		EntryName:     entryName,
		UserID:        userID,
		DirectoryName: directoryName,
		Files:         []goupload.DirectoryFileEntry{},
	}

	// 遍历本地目录，收集所有文件
	err := filepath.Walk(localDirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil || info.IsDir() {
			return err
		}

		// 计算相对路径
		relPath, err := filepath.Rel(localDirPath, path)
		if err != nil {
			return err
		}

		// 打开文件
		file, err := os.Open(path)
		if err != nil {
			return err
		}
		defer file.Close()

		request.Files = append(request.Files, goupload.DirectoryFileEntry{
			RelativePath: filepath.ToSlash(relPath), // 统一使用正斜杠
			Reader:       file,
			Size:         info.Size(),
			ModTime:      &info.ModTime(),
		})

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("遍历目录失败: %w", err)
	}

	// 执行目录上传
	return goupload.UploadDirectory(ctx, statsUpdater, request)
}
