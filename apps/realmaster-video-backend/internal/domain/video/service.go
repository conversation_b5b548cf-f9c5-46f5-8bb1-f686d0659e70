package video

import (
	"context"
	"errors"
	"fmt"
	"io"
	"math"
	"mime/multipart"
	"os"
	"path/filepath"
	"realmaster-video-backend/internal/config"
	"realmaster-video-backend/internal/domain/common"
	"realmaster-video-backend/internal/platform/logger"

	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/real-rm/gofile/levelStore"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

const unpublishedPrefix = "unpublished_"

var (
	ErrVideoAlreadyPublished     = errors.New("视频已被发布")
	ErrInvalidVideoStatus        = errors.New("视频状态不正确，无法执行操作")
	ErrVideoInProcessing         = errors.New("无法修改正在处理中的视频")
	ErrCannotDeleteProcessing    = errors.New("无法删除正在处理中的视频，请稍后再试")
	ErrInvalidVideoID            = errors.New("无效的视频ID格式")
	ErrVideoFileRequired         = errors.New("视频文件不能为空")
	ErrThumbnailRequired         = errors.New("封面图片不能为空")
	ErrInvalidRepublishOperation = errors.New("无效的操作: 'republish' 动作必须与新上传的视频文件一起使用")
)

// Service 定义了视频相关的业务逻辑接口
type Service interface {
	CreateDraft(ctx context.Context, req CreateVideoRequest) (*Video, error)
	PublishVideo(ctx context.Context, videoID string) error
	GetVideoByID(ctx context.Context, videoID string) (*VideoResponse, error)
	UpdateStats(ctx context.Context, videoID string, req UpdateStatsRequest) error
	FindVideos(ctx context.Context, filter VideoFilter) ([]VideoResponse, *common.Pagination, error)
	GetVideoStats(ctx context.Context, filter VideoFilter) (*VideoStatsSummary, error)
	DeleteVideo(ctx context.Context, id string) error
	UpdateVideo(ctx context.Context, req UpdateVideoRequest) (*Video, error)
	// 未来可添加的方法: UpdateDraft, ListVideos, 等.
}

type service struct {
	repo Repository
	cfg  *config.Config
}

// NewService 创建一个新的视频服务实例
func NewService(repo Repository, cfg *config.Config) Service {
	return &service{
		repo: repo,
		cfg:  cfg,
	}
}

// GetVideoByID 通过ID获取单个视频
func (s *service) GetVideoByID(ctx context.Context, videoID string) (*VideoResponse, error) {
	video, err := s.repo.FindByID(ctx, videoID)
	if err != nil {
		return nil, err
	}
	return s.toVideoResponse(video), nil
}

// UpdateStatsRequest 定义了更新视频统计数据的请求结构
// 使用指针类型以支持部分更新，客户端可以只发送需要增加的字段
type UpdateStatsRequest struct {
	Views       *int64 `json:"views"`
	Likes       *int64 `json:"likes"`
	Collections *int64 `json:"collections"`
	Completions *int64 `json:"completions"`
}

// UpdateStats 调用仓库层来原子性地更新视频统计信息
func (s *service) UpdateStats(ctx context.Context, videoID string, req UpdateStatsRequest) error {
	var views, likes, collections, completions int64

	// 如果指针非nil，则使用其值，否则默认为0
	if req.Views != nil {
		views = *req.Views
	}
	if req.Likes != nil {
		likes = *req.Likes
	}
	if req.Collections != nil {
		collections = *req.Collections
	}
	if req.Completions != nil {
		completions = *req.Completions
	}

	// 在这里可以添加业务逻辑验证，例如检查值是否为负数等
	if views < 0 || likes < 0 || collections < 0 || completions < 0 {
		return errors.New("统计增量不能为负数")
	}

	return s.repo.IncrementStats(
		ctx,
		videoID,
		views,
		likes,
		collections,
		completions,
	)
}

// CreateDraft handles the logic of creating a new video draft.
func (s *service) CreateDraft(ctx context.Context, req CreateVideoRequest) (*Video, error) {

	// 验证并转换 CategoryID 和 ClientID
	var categoryObjectID, clientObjectID primitive.ObjectID
	var err error
	if req.CategoryID != "" {
		categoryObjectID, err = primitive.ObjectIDFromHex(req.CategoryID)
		if err != nil {
			return nil, fmt.Errorf("%w: %w", common.ErrInvalidCategoryID, err)
		}
	}
	if req.ClientID != "" {
		clientObjectID, err = primitive.ObjectIDFromHex(req.ClientID)
		if err != nil {
			return nil, fmt.Errorf("%w: %w", common.ErrInvalidClientID, err)
		}
	}

	video := &Video{
		ID:          primitive.NewObjectID(),
		Title:       req.Title,       // 使用新的嵌套结构
		Description: req.Description, // 使用新的嵌套结构
		UploaderID:  req.UploaderID,
		CategoryID:  categoryObjectID,
		Tags:        req.Tags,
		PropertyIDs: req.PropertyIDs,
		ExternalURL: req.ExternalURL,
		ClientID:    clientObjectID,
		// 初始化 status, stats, timestamps 等
		DraftThumbPath: req.DraftThumbFilePath,
		DraftVideoPath: req.DraftVideoFilePath,
		// 构建草稿文件的访问URL
		DraftVideoURL: s.buildDraftVideoURL(req.DraftVideoGouploadPath),
		DraftThumbURL: func() string {
			if req.DraftThumbGouploadPath != "" {
				return s.buildDraftThumbURL(req.DraftThumbGouploadPath)
			}
			return ""
		}(),
		Stats: VideoStats{
			Views:          0,
			Likes:          0,
			Collections:    0,
			Completions:    0,
			CompletionRate: "0.0%",
		},
		// 移除手动时间戳，gomongo会自动添加_ts和_mt
	}

	// 根据 'publishNow' 标志决定初始状态
	if req.PublishNow {
		video.Status = StatusPending // 直接设置为 "Pending"
	} else {
		video.Status = StatusDraft // 默认为 "Draft"
	}

	if err := s.repo.Create(ctx, video); err != nil {
		return nil, err
	}

	return video, nil
}

// PublishVideo 将视频状态更新为 "Pending" 以便 worker 处理
func (s *service) PublishVideo(ctx context.Context, videoID string) error {
	video, err := s.repo.FindByID(ctx, videoID)
	if err != nil {
		return err
	}

	// 1. 检查视频是否可以被发布
	// 只有草稿或处理失败的视频才能被重新发布
	if video.Status != StatusDraft && video.Status != StatusProcessingFailed {
		return fmt.Errorf("%w: 当前状态为 '%s'", ErrInvalidVideoStatus, video.Status)
	}

	// 2. 更新状态为"待处理"
	// Worker 将会轮询这个状态的视频
	video.Status = StatusPending
	// 移除手动时间戳设置，gomongo会自动更新_mt
	if err := s.repo.Update(ctx, video); err != nil {
		return fmt.Errorf("更新视频状态为待处理失败: %w", err)
	}

	return nil
}

// FindVideos retrieves a paginated list of videos based on filters.
func (s *service) FindVideos(ctx context.Context, filter VideoFilter) ([]VideoResponse, *common.Pagination, error) {
	videos, total, err := s.repo.Find(ctx, filter)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to find videos: %w", err)
	}

	// Convert DB models to API response DTOs
	videoResponses := make([]VideoResponse, len(videos))
	for i := range videos {
		videoResponses[i] = *s.toVideoResponse(&videos[i])
	}

	var totalPages int64
	if filter.Limit > 0 {
		totalPages = int64(math.Ceil(float64(total) / float64(filter.Limit)))
	}

	pagination := &common.Pagination{
		TotalItems:  total,
		TotalPages:  totalPages,
		CurrentPage: int64(filter.Page),
		Limit:       int64(filter.Limit),
	}

	return videoResponses, pagination, nil
}

// GetVideoStats retrieves aggregated video stats based on the filter.
func (s *service) GetVideoStats(ctx context.Context, filter VideoFilter) (*VideoStatsSummary, error) {
	return s.repo.GetAggregatedStats(ctx, filter)
}

// getFinalMediaDirPath returns the absolute path for a video's media directory
// based on a given status using L1/L2 hierarchical structure.
func (s *service) getFinalMediaDirPath(videoIDHex string, status string) string {
	// 从ObjectID中提取创建时间（ObjectID包含时间戳信息）
	objectID, err := primitive.ObjectIDFromHex(videoIDHex)
	if err != nil {
		logger.Log.Error("Invalid ObjectID, falling back to legacy path",
			logger.String("videoID", videoIDHex), logger.Error(err))
		dirName := videoIDHex
		if status == StatusUnpublished {
			dirName = unpublishedPrefix + dirName
		}
		return filepath.Join(s.cfg.Media.StorageDir, dirName)
	}

	createdAt := objectID.Timestamp()
	// 使用gofile包生成L1/L2路径 - 视频文件使用EDM board
	l1, l2, err := levelStore.GetL1L2Separate(createdAt, "EDM", videoIDHex)
	if err != nil {
		// 如果L1/L2生成失败，回退到原有逻辑
		logger.Log.Error("Failed to generate L1/L2 path, falling back to legacy path",
			logger.String("videoID", videoIDHex), logger.Error(err))
		dirName := videoIDHex
		if status == StatusUnpublished {
			dirName = unpublishedPrefix + dirName
		}
		return filepath.Join(s.cfg.Media.StorageDir, dirName)
	}

	// 构建L1/L2分层目录结构
	dirName := videoIDHex
	if status == StatusUnpublished {
		dirName = unpublishedPrefix + dirName
	}
	return filepath.Join(s.cfg.Media.StorageDir, "EDM", l1, l2, dirName)
}

// getFinalMediaRelativePath returns the relative path for URL construction
// This will be used for building media URLs that work with L1/L2 structure
func (s *service) getFinalMediaRelativePath(videoIDHex string, status string) string {
	// 从ObjectID中提取创建时间（ObjectID包含时间戳信息）
	objectID, err := primitive.ObjectIDFromHex(videoIDHex)
	if err != nil {
		logger.Log.Error("Invalid ObjectID for relative path, falling back to legacy path",
			logger.String("videoID", videoIDHex), logger.Error(err))
		dirName := videoIDHex
		if status == StatusUnpublished {
			dirName = unpublishedPrefix + dirName
		}
		return dirName
	}

	createdAt := objectID.Timestamp()
	// 使用gofile包生成L1/L2路径 - 视频文件使用EDM board
	l1, l2, err := levelStore.GetL1L2Separate(createdAt, "EDM", videoIDHex)
	if err != nil {
		// 如果L1/L2生成失败，回退到原有逻辑
		logger.Log.Error("Failed to generate L1/L2 relative path, falling back to legacy path",
			logger.String("videoID", videoIDHex), logger.Error(err))
		dirName := videoIDHex
		if status == StatusUnpublished {
			dirName = unpublishedPrefix + dirName
		}
		return dirName
	}

	// 构建L1/L2分层相对路径
	dirName := videoIDHex
	if status == StatusUnpublished {
		dirName = unpublishedPrefix + dirName
	}
	return filepath.Join("EDM", l1, l2, dirName)
}

// deleteAssociatedFiles attempts to delete all files associated with a video object.
// It returns a single error if any of the file operations fail.
func (s *service) deleteAssociatedFiles(video *Video) error {
	var errorMessages []string

	// 1. Delete temporary files (if they exist) and cleanup empty directories
	if video.DraftThumbPath != "" {
		if err := os.Remove(video.DraftThumbPath); err != nil && !os.IsNotExist(err) {
			logger.Log.Error("Failed to delete temp thumbnail", logger.String("path", video.DraftThumbPath), logger.Error(err))
			errorMessages = append(errorMessages, fmt.Sprintf("failed to delete temp thumbnail: %s", video.DraftThumbPath))
		} else {
			// 清理临时文件的空目录
			tempDir := filepath.Dir(video.DraftThumbPath)
			s.cleanupEmptyDirectories(tempDir, s.cfg.Server.DraftDir)
		}
	}
	if video.DraftVideoPath != "" {
		if err := os.Remove(video.DraftVideoPath); err != nil && !os.IsNotExist(err) {
			logger.Log.Error("Failed to delete temp video", logger.String("path", video.DraftVideoPath), logger.Error(err))
			errorMessages = append(errorMessages, fmt.Sprintf("failed to delete temp video: %s", video.DraftVideoPath))
		} else {
			// 清理临时文件的空目录
			tempDir := filepath.Dir(video.DraftVideoPath)
			s.cleanupEmptyDirectories(tempDir, s.cfg.Server.DraftDir)
		}
	}

	// 2. Delete the final media directory, which contains processed files (HLS, etc.)
	finalMediaDir := s.getFinalMediaDirPath(video.ID.Hex(), video.Status)
	if _, err := os.Stat(finalMediaDir); !os.IsNotExist(err) {
		if err := os.RemoveAll(finalMediaDir); err != nil {
			logger.Log.Error("Failed to delete final media directory", logger.String("path", finalMediaDir), logger.Error(err))
			errorMessages = append(errorMessages, fmt.Sprintf("failed to delete final media directory: %s", finalMediaDir))
		} else {
			logger.Log.Info("Successfully deleted final media directory", logger.String("path", finalMediaDir))
			// 清理最终媒体文件的空目录
			parentDir := filepath.Dir(finalMediaDir)
			s.cleanupEmptyDirectories(parentDir, s.cfg.Media.StorageDir)
		}
	}

	if len(errorMessages) > 0 {
		return fmt.Errorf("encountered errors during file deletion: %s", strings.Join(errorMessages, "; "))
	}

	return nil
}

// DeleteVideo handles the logic for deleting a video and its associated files.
// The operation is atomic: it first deletes files, and only upon success, deletes the database record.
func (s *service) DeleteVideo(ctx context.Context, id string) error {
	// 1. Get video info from DB to know which files to delete.
	video, err := s.repo.FindByID(ctx, id)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			// If the DB record doesn't exist, the desired state is already achieved.
			// This is not an error in the context of a delete operation.
			return nil
		}
		return err // Handle other potential database errors.
	}

	// 2. Perform business logic checks. For example, prevent deletion of a video that is actively being processed.
	if video.Status == StatusProcessing {
		return ErrCannotDeleteProcessing
	}

	// 3. Delete all associated files from the filesystem FIRST.
	// This is the most critical step. If this fails, we abort and leave the DB record intact for a retry.
	if err := s.deleteAssociatedFiles(video); err != nil {
		return fmt.Errorf("failed to delete associated files, database record is preserved for retry: %w", err)
	}

	// 4. Only after all files are successfully deleted, delete the database record.
	if err := s.repo.DeleteByID(ctx, video.ID); err != nil {
		// This is a critical failure state where files are gone but the DB record remains.
		// Log as error but don't crash the application
		logger.Log.Error("CRITICAL: Files deleted but failed to delete DB record - manual intervention required",
			logger.String("videoID", id),
			logger.String("status", "files_deleted_db_failed"),
			logger.Error(err),
		)
		// TODO: 考虑实现补偿机制，如将记录标记为"待清理"状态
		return fmt.Errorf("files were deleted, but failed to delete the database record. Manual intervention required for video ID '%s': %w", id, err)
	}

	return nil
}

// toVideoResponse converts a Video DB model to a VideoResponse API DTO.
// This is where we populate the virtual preview URL fields.
func (s *service) toVideoResponse(video *Video) *VideoResponse {
	resp := &VideoResponse{
		ID:              video.ID,
		Title:           video.Title,
		Description:     video.Description,
		Status:          video.Status,
		Duration:        video.Duration,
		UploaderID:      video.UploaderID,
		CategoryID:      video.CategoryID,
		Tags:            video.Tags,
		PropertyIDs:     video.PropertyIDs,
		ExternalURL:     video.ExternalURL,
		ClientID:        video.ClientID,
		Stats:           video.Stats,
		ProcessingError: video.ProcessingError,
		// 移除手动时间戳字段，如需要可从gomongo的_ts和_mt获取
		// CreatedAt:       video.CreatedAt,
		// UpdatedAt:       video.UpdatedAt,
		PublishedAt: video.PublishedAt,
	}

	switch video.Status {
	case StatusDraft, StatusPending, StatusProcessing, StatusProcessingFailed:
		// 对于这些临时状态，直接使用存储的URL
		if video.DraftThumbURL != "" {
			resp.PreviewThumbUrl = video.DraftThumbURL
		}
		if video.DraftVideoURL != "" {
			resp.PreviewVideoUrl = video.DraftVideoURL
		}
	case StatusPublished, StatusUnpublished:
		// 对于这些最终状态，我们构建完整的、可公开访问的URL
		// 使用新的相对路径函数来支持L1/L2结构
		dirComponent := s.getFinalMediaRelativePath(video.ID.Hex(), video.Status)
		if video.ThumbURL != "" {
			// 使用 filepath.Base 确保我们只传递文件名，防止路径重复
			resp.PreviewThumbUrl = s.buildFinalMediaURL(dirComponent, filepath.Base(video.ThumbURL))
		}
		if video.HlsURL != "" {
			// 对于已处理的视频，previewVideoUrl返回HLS文件路径
			resp.PreviewVideoUrl = s.buildFinalMediaURL(dirComponent, filepath.Base(video.HlsURL))
		}
	}
	return resp
}

// UpdateVideo handles the complex logic of updating a video.
func (s *service) UpdateVideo(ctx context.Context, req UpdateVideoRequest) (*Video, error) {
	// 1. Get current video state
	video, err := s.repo.FindByID(ctx, req.ID.Hex())
	if err != nil {
		return nil, err // Returns ErrVideoNotFound if applicable
	}

	// 2. Check for permissions based on state
	// Note: 这里存在TOCTOU问题，但由于MongoDB的单文档操作是原子的，
	// 实际的状态检查会在repository层的Update操作中再次进行
	switch video.Status {
	case StatusPending, StatusProcessing:
		return nil, ErrVideoInProcessing
	}

	updateData := bson.M{}
	var newStatus string // To hold the definitive new status

	// 3. Process file updates (if a new video file is provided)
	if req.DraftVideoFile != nil {
		var newTempThumbPath string // We need to determine this before deleting files

		// --- Step 1 (Pre-emptive): Handle thumbnail copy if needed ---
		// If no new thumbnail is provided AND the video was previously published,
		// we must copy the old final thumbnail BEFORE deleting the media directory.
		if req.DraftThumbnailFile == nil && (video.Status == StatusPublished || video.Status == StatusUnpublished) {
			if video.ThumbURL != "" {
				finalThumbFilename := filepath.Base(video.ThumbURL)
				// 使用L1/L2路径获取旧封面的完整路径
				mediaDir := s.getFinalMediaDirPath(video.ID.Hex(), video.Status)
				finalThumbPath := filepath.Join(mediaDir, finalThumbFilename)

				var err error
				newTempThumbPath, err = s.copyToTempFile(finalThumbPath, "thumb-")
				if err != nil && !errors.Is(err, os.ErrNotExist) {
					// Log the error but don't fail the whole process, as the video file is more critical.
					logger.Log.Warn("无法在删除前复制旧封面到临时位置", logger.Error(err), logger.String("path", finalThumbPath))
				} else if newTempThumbPath != "" {
					logger.Log.Info("已成功在删除前复制旧封面到临时位置", logger.String("path", newTempThumbPath))
				}
			}
		}

		// --- Step 2: Determine new status ---
		if req.Action == "publish" || req.Action == "republish" {
			newStatus = StatusPending
		} else { // "save_as_draft"
			newStatus = StatusDraft
		}
		updateData["st"] = newStatus

		// --- Step 3: Clean up old final media directory ---
		if video.Status == StatusPublished || video.Status == StatusUnpublished {
			mediaDir := s.getFinalMediaDirPath(video.ID.Hex(), video.Status)
			if _, statErr := os.Stat(mediaDir); !os.IsNotExist(statErr) {
				logger.Log.Info("Republishing: removing old media directory", logger.String("path", mediaDir))
				if removeErr := os.RemoveAll(mediaDir); removeErr != nil {
					return nil, fmt.Errorf("删除旧媒体目录失败: %w", removeErr)
				} else {
					// 清理空目录
					parentDir := filepath.Dir(mediaDir)
					s.cleanupEmptyDirectories(parentDir, s.cfg.Media.StorageDir)
				}
			}
		}

		// --- Step 4: Save new video file ---
		if video.DraftVideoPath != "" {
			os.Remove(video.DraftVideoPath)
		}
		newDraftVideoPath, err := s.saveDraftFile(req.DraftVideoFile, "video-")
		if err != nil {
			return nil, fmt.Errorf("保存新临时视频文件失败: %w", err)
		}
		updateData["tempVideoPath"] = newDraftVideoPath

		// --- Step 5: Clear all old processing-related DB fields ---
		updateData["hlsUrl"] = ""
		updateData["thumbUrl"] = ""
		updateData["procErr"] = ""
		updateData["drt"] = 0
		updateData["manifestsBaseName"] = ""

		// --- Step 6: Set final temp thumbnail path ---
		var finalTempThumbPath string
		// Case A: A new thumbnail is uploaded. This takes top priority.
		if req.DraftThumbnailFile != nil {
			// If there was an old temp thumb on the draft, remove its file.
			if video.DraftThumbPath != "" {
				os.Remove(video.DraftThumbPath)
			}
			path, err := s.saveDraftFile(req.DraftThumbnailFile, "thumb-")
			if err != nil {
				return nil, fmt.Errorf("保存新临时封面失败: %w", err)
			}
			finalTempThumbPath = path
		} else if newTempThumbPath != "" {
			// Case B: No new thumb, but we pre-emptively copied a final thumb from a Published video.
			finalTempThumbPath = newTempThumbPath
		} else {
			// Case C: No new thumb, no pre-copied thumb, so PRESERVE the original temp thumb from the draft.
			finalTempThumbPath = video.DraftThumbPath
		}
		updateData["tempThumbPath"] = finalTempThumbPath

	} else {
		// Logic for when NO new video file is uploaded
		originalStatus := video.Status
		newStatus = originalStatus
		switch req.Action {
		case "publish":
			// If coming from Unpublished, go directly to Published.
			// Otherwise, it needs processing.
			switch originalStatus {
			case StatusUnpublished:
				newStatus = StatusPublished
				// --- RENAME LOGIC ---
				oldPath := s.getFinalMediaDirPath(video.ID.Hex(), StatusUnpublished)
				newPath := s.getFinalMediaDirPath(video.ID.Hex(), StatusPublished)
				if _, err := os.Stat(oldPath); !os.IsNotExist(err) {
					// 检查目标路径是否已存在，避免冲突
					if _, err := os.Stat(newPath); !os.IsNotExist(err) {
						logger.Log.Warn("目标路径已存在，将先删除", logger.String("path", newPath))
						if err := os.RemoveAll(newPath); err != nil {
							return nil, fmt.Errorf("删除已存在的目标路径失败: %w", err)
						}
					}

					if err := os.Rename(oldPath, newPath); err != nil {
						logger.Log.Error("failed to rename media directory for publish", logger.Error(err), logger.String("from", oldPath), logger.String("to", newPath))
						return nil, fmt.Errorf("failed to rename media directory: %w", err)
					}
					logger.Log.Info("renamed media directory for publish", logger.String("from", oldPath), logger.String("to", newPath))
				}
				// --- END RENAME LOGIC ---
			case StatusDraft, StatusProcessingFailed:
				newStatus = StatusPending
			}
		case "unpublish":
			if originalStatus == StatusPublished {
				newStatus = StatusUnpublished
				// --- RENAME LOGIC ---
				oldPath := s.getFinalMediaDirPath(video.ID.Hex(), StatusPublished)
				newPath := s.getFinalMediaDirPath(video.ID.Hex(), StatusUnpublished)
				if _, err := os.Stat(oldPath); !os.IsNotExist(err) {
					// 检查目标路径是否已存在，避免冲突
					if _, err := os.Stat(newPath); !os.IsNotExist(err) {
						logger.Log.Warn("目标路径已存在，将先删除", logger.String("path", newPath))
						if err := os.RemoveAll(newPath); err != nil {
							return nil, fmt.Errorf("删除已存在的目标路径失败: %w", err)
						}
					}

					if err := os.Rename(oldPath, newPath); err != nil {
						logger.Log.Error("failed to rename media directory for unpublish", logger.Error(err), logger.String("from", oldPath), logger.String("to", newPath))
						return nil, fmt.Errorf("failed to rename media directory: %w", err)
					}
					logger.Log.Info("renamed media directory for unpublish", logger.String("from", oldPath), logger.String("to", newPath))
				}
				// --- END RENAME LOGIC ---
			}
		case "republish":
			// It is illegal to "republish" without providing a new video file.
			// This action is reserved for replacing the video content.
			return nil, ErrInvalidRepublishOperation
		}
		if newStatus != originalStatus {
			updateData["st"] = newStatus
			if newStatus == StatusPublished {
				updateData["pts"] = time.Now()
			}
		}
		// Also handle standalone thumbnail update
		if req.DraftThumbnailFile != nil {
			// CRITICAL: Differentiate behavior based on video status.
			// We use `newStatus` because the directory might have been renamed
			// earlier in this function based on the action (e.g., publish/unpublish).
			if newStatus == StatusPublished || newStatus == StatusUnpublished {
				// --- Direct Final Thumbnail Update ---
				// Use `newStatus` to get the correct path after a potential rename.
				mediaDir := s.getFinalMediaDirPath(video.ID.Hex(), newStatus)
				// 1. Clean up old FINAL thumbnail file if it exists
				if video.ThumbURL != "" {
					oldThumbFilename := filepath.Base(video.ThumbURL)
					oldThumbPath := filepath.Join(mediaDir, oldThumbFilename)
					if err := os.Remove(oldThumbPath); err != nil && !os.IsNotExist(err) {
						logger.Log.Warn("无法删除旧的最终封面", logger.Error(err), logger.String("path", oldThumbPath))
					}
				}

				// 2. Save new thumbnail directly to FINAL location with a new unique name.
				// We create a unique name to avoid browser caching issues.
				newThumbFilename := "thumb-" + primitive.NewObjectID().Hex() + filepath.Ext(req.DraftThumbnailFile.Filename)
				finalThumbPath := filepath.Join(mediaDir, newThumbFilename)

				if err := s.saveFinalFile(req.DraftThumbnailFile, finalThumbPath); err != nil {
					return nil, fmt.Errorf("保存新封面到最终位置失败: %w", err)
				}
				// 3. Update the `thumbUrl` field in the database with the new filename
				updateData["thumbUrl"] = newThumbFilename

			} else {
				// --- Temporary Thumbnail Update (for Drafts, Failed, etc.) ---
				if video.DraftThumbPath != "" {
					os.Remove(video.DraftThumbPath)
				}
				newDraftThumbPath, err := s.saveDraftFile(req.DraftThumbnailFile, "thumb-")
				if err != nil {
					return nil, fmt.Errorf("保存新临时封面失败: %w", err)
				}
				updateData["tempThumbPath"] = newDraftThumbPath
			}
		}
	}

	// Process metadata updates from a generic map.
	for key, value := range req.Metadata {
		switch key {
		case "title": // JSON key from frontend
			updateData["tl"] = value // BSON key
		case "description":
			updateData["dsc"] = value
		case "tags":
			updateData["tags"] = value
		case "propertyIds":
			updateData["prop"] = value
		case "externalUrl":
			updateData["ExtUrl"] = value // BSON key
		case "categoryId":
			if catIDStr, ok := value.(string); ok && catIDStr != "" {
				catID, err := primitive.ObjectIDFromHex(catIDStr)
				if err != nil {
					return nil, fmt.Errorf("%w: %w", common.ErrInvalidCategoryID, err)
				}
				updateData["catId"] = catID // BSON key
			} else {
				updateData["catId"] = nil
			}
		case "clientId": // JSON key from frontend
			if clientIDStr, ok := value.(string); ok && clientIDStr != "" {
				clientID, err := primitive.ObjectIDFromHex(clientIDStr)
				if err != nil {
					return nil, fmt.Errorf("%w: %w", common.ErrInvalidClientID, err)
				}
				updateData["ClntId"] = clientID // BSON key
			} else {
				updateData["ClntId"] = nil
			}
		default:
			// Optionally log or ignore unhandled keys
		}
	}

	// 6. Apply updates to the database
	if len(updateData) > 0 {
		// gomongo会自动更新_mt字段
		if err := s.repo.UpdateSelective(ctx, req.ID, updateData); err != nil {
			return nil, err
		}
	}

	// 7. Return the updated video object
	return s.repo.FindByID(ctx, req.ID.Hex())
}

func (s *service) copyToTempFile(srcPath, prefix string) (string, error) {
	srcFile, err := os.Open(srcPath)
	if err != nil {
		return "", fmt.Errorf("无法打开源文件 %s: %w", srcPath, err)
	}
	defer srcFile.Close()

	tempFile, err := os.CreateTemp(s.cfg.Server.DraftDir, prefix+"*")
	if err != nil {
		return "", fmt.Errorf("无法创建临时文件: %w", err)
	}
	defer tempFile.Close()

	_, err = io.Copy(tempFile, srcFile)
	if err != nil {
		return "", fmt.Errorf("无法复制文件内容: %w", err)
	}

	return tempFile.Name(), nil
}

// --- Helper functions for file saving ---

func (s *service) saveDraftFile(fileHeader *multipart.FileHeader, prefix string) (string, error) {
	file, err := fileHeader.Open()
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 使用当前时间和随机ID生成L1/L2路径
	now := time.Now()
	randomID := uuid.New().String()

	// 使用gofile包生成L1/L2路径 - 临时视频文件使用EDM board
	l1, l2, err := levelStore.GetL1L2Separate(now, "EDM", randomID)
	if err != nil {
		// 如果L1/L2生成失败，回退到原有逻辑
		tempFile, err := os.CreateTemp(s.cfg.Server.DraftDir, prefix+"*")
		if err != nil {
			return "", err
		}
		defer tempFile.Close()

		_, err = io.Copy(tempFile, file)
		if err != nil {
			return "", err
		}
		return tempFile.Name(), nil
	}

	// 构建L1/L2分层目录结构
	tempL1L2Dir := filepath.Join(s.cfg.Server.DraftDir, "EDM", l1, l2)
	if err := os.MkdirAll(tempL1L2Dir, 0755); err != nil {
		return "", fmt.Errorf("创建临时L1/L2目录失败: %w", err)
	}

	// 生成唯一文件名
	uniqueFilename := prefix + randomID + "*"
	tempFile, err := os.CreateTemp(tempL1L2Dir, uniqueFilename)
	if err != nil {
		return "", err
	}
	defer tempFile.Close()

	_, err = io.Copy(tempFile, file)
	if err != nil {
		return "", err
	}
	return tempFile.Name(), nil
}

func (s *service) saveFinalFile(fileHeader *multipart.FileHeader, finalPath string) error {
	file, err := fileHeader.Open()
	if err != nil {
		return err
	}
	defer file.Close()

	// Ensure the directory exists
	if err := os.MkdirAll(filepath.Dir(finalPath), 0755); err != nil {
		return err
	}

	dst, err := os.Create(finalPath)
	if err != nil {
		return err
	}
	defer dst.Close()

	_, err = io.Copy(dst, file)
	return err
}

func (s *service) buildFinalMediaURL(relativePath, fileName string) string {
	// 确保基础URL包含协议，以生成一个绝对URL
	baseURL := s.cfg.Media.ServerURL
	if !strings.HasPrefix(baseURL, "http://") && !strings.HasPrefix(baseURL, "https://") {
		baseURL = "http://" + baseURL
	}
	// 不能使用 filepath.Join 来构建URL，因为它会错误处理 "http://"
	// 应该使用简单的字符串格式化，relativePath已经包含了L1/L2结构
	return fmt.Sprintf("%s/media/%s/%s", baseURL, relativePath, fileName)
}

// buildDraftVideoURL 构建草稿视频文件的完整URL（通过nginx访问）
func (s *service) buildDraftVideoURL(gouploadPath string) string {
	// 确保基础URL包含协议
	baseURL := s.cfg.Media.ServerURL
	if !strings.HasPrefix(baseURL, "http://") && !strings.HasPrefix(baseURL, "https://") {
		baseURL = "http://" + baseURL
	}
	// gouploadPath 格式: USER/2025-28/abc123/video-xxx.mp4
	return fmt.Sprintf("%s/draft/videos/%s", baseURL, gouploadPath)
}

// buildDraftThumbURL 构建草稿缩略图文件的完整URL（通过nginx访问）
func (s *service) buildDraftThumbURL(gouploadPath string) string {
	// 确保基础URL包含协议
	baseURL := s.cfg.Media.ServerURL
	if !strings.HasPrefix(baseURL, "http://") && !strings.HasPrefix(baseURL, "https://") {
		baseURL = "http://" + baseURL
	}
	// gouploadPath 格式: USER/2025-28/abc123/thumb-xxx.jpg
	return fmt.Sprintf("%s/draft/thumbnails/%s", baseURL, gouploadPath)
}

// cleanupEmptyDirectories 递归清理空目录
// 从给定路径开始向上清理，直到遇到非空目录或到达根目录
func (s *service) cleanupEmptyDirectories(dirPath string, rootDir string) {
	common.CleanupEmptyDirectories(dirPath, rootDir)
}
