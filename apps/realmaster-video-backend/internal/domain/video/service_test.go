package video

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"realmaster-video-backend/internal/testutil"
)

func TestVideoService_CreateDraft(t *testing.T) {
	// 设置测试环境
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()
	suite.CreateTempDirs()

	// 创建service实例
	repo := NewRepository()
	service := NewService(repo, suite.Config)
	ctx := context.Background()

	t.Run("成功创建草稿视频", func(t *testing.T) {
		// 准备测试数据
		req := CreateVideoRequest{
			Title: MultilingualString{
				Zh: "测试视频创建",
				En: "Test Video Creation",
			},
			Description: MultilingualString{
				Zh: "这是一个测试视频创建请求",
				En: "This is a test video creation request",
			},
			UploaderID:         "test-uploader",
			CategoryID:         primitive.NewObjectID().Hex(),
			Tags:               []string{"测试", "创建"},
			PropertyIDs:        []string{"prop-123"},
			ExternalURL:        "https://example.com/property/456",
			ClientID:           primitive.NewObjectID().Hex(),
			DraftVideoFilePath: "/tmp/test_video.mp4",
			DraftThumbFilePath: "/tmp/test_thumb.jpg",
		}

		// 执行创建操作
		video, err := service.CreateDraft(ctx, req)

		// 验证结果
		require.NoError(t, err)
		require.NotNil(t, video)
		assert.Equal(t, StatusDraft, video.Status)
		assert.Equal(t, req.Title.Zh, video.Title.Zh)
		assert.Equal(t, req.UploaderID, video.UploaderID)
		assert.False(t, video.ID.IsZero())

		// 验证数据库中的数据
		foundVideo, err := repo.FindByID(ctx, video.ID.Hex())
		require.NoError(t, err)
		assert.Equal(t, video.ID, foundVideo.ID)
		assert.Equal(t, StatusDraft, foundVideo.Status)
	})

	t.Run("无效分类ID创建失败", func(t *testing.T) {
		req := CreateVideoRequest{
			Title: MultilingualString{
				Zh: "测试视频",
				En: "Test Video",
			},
			UploaderID: "test-uploader",
			CategoryID: "invalid-id", // 无效的ObjectID格式
		}

		// 执行创建操作
		video, err := service.CreateDraft(ctx, req)

		// 验证结果
		assert.Error(t, err)
		assert.Nil(t, video)
	})

	t.Run("空标题创建成功但标题为空", func(t *testing.T) {
		req := CreateVideoRequest{
			// Title为空，但其他字段正常
			UploaderID: "test-uploader",
			CategoryID: primitive.NewObjectID().Hex(),
		}

		// 执行创建操作
		video, err := service.CreateDraft(ctx, req)

		// 验证结果 - 根据实际实现，可能允许空标题
		require.NoError(t, err)
		require.NotNil(t, video)
		assert.Equal(t, StatusDraft, video.Status)
		assert.Equal(t, "", video.Title.Zh) // 标题为空
		assert.Equal(t, "", video.Title.En)
	})
}

func TestVideoService_GetVideoByID(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	service := NewService(repo, suite.Config)
	ctx := context.Background()

	t.Run("成功获取存在的视频", func(t *testing.T) {
		// 先创建一个视频
		originalVideo := &Video{
			ID: primitive.NewObjectID(),
			Title: MultilingualString{
				Zh: "获取测试视频",
				En: "Get Test Video",
			},
			Description: MultilingualString{
				Zh: "测试描述",
				En: "Test Description",
			},
			Status:     StatusPublished,
			Duration:   120.5,
			UploaderID: "test-uploader",
			CategoryID: primitive.NewObjectID(),
			Tags:       []string{"测试"},
			Stats: VideoStats{
				Views:          100,
				Likes:          10,
				Collections:    5,
				Completions:    80,
				CompletionRate: "80.0%",
			},
		}

		err := repo.Create(ctx, originalVideo)
		require.NoError(t, err)

		// 通过服务获取视频
		videoResponse, err := service.GetVideoByID(ctx, originalVideo.ID.Hex())

		// 验证结果
		require.NoError(t, err)
		require.NotNil(t, videoResponse)
		assert.Equal(t, originalVideo.ID, videoResponse.ID)
		assert.Equal(t, originalVideo.Title.Zh, videoResponse.Title.Zh)
		assert.Equal(t, originalVideo.Status, videoResponse.Status)
		assert.Equal(t, originalVideo.Duration, videoResponse.Duration)
		assert.Equal(t, originalVideo.Stats.Views, videoResponse.Stats.Views)
	})

	t.Run("获取不存在的视频返回错误", func(t *testing.T) {
		nonExistentID := primitive.NewObjectID().Hex()

		videoResponse, err := service.GetVideoByID(ctx, nonExistentID)

		assert.Error(t, err)
		assert.Equal(t, ErrVideoNotFound, err)
		assert.Nil(t, videoResponse)
	})

	t.Run("无效ID格式返回错误", func(t *testing.T) {
		invalidID := "invalid-id-format"

		videoResponse, err := service.GetVideoByID(ctx, invalidID)

		assert.Error(t, err)
		assert.Nil(t, videoResponse)
	})
}

func TestVideoService_PublishVideo(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	service := NewService(repo, suite.Config)
	ctx := context.Background()

	t.Run("成功发布草稿视频", func(t *testing.T) {
		// 创建草稿视频
		draftVideo := &Video{
			ID: primitive.NewObjectID(),
			Title: MultilingualString{
				Zh: "待发布视频",
				En: "Video to Publish",
			},
			Status:     StatusDraft,
			UploaderID: "test-uploader",
		}

		err := repo.Create(ctx, draftVideo)
		require.NoError(t, err)

		// 发布视频
		err = service.PublishVideo(ctx, draftVideo.ID.Hex())

		// 验证结果
		require.NoError(t, err)

		// 验证状态已更新为pending
		updatedVideo, err := repo.FindByID(ctx, draftVideo.ID.Hex())
		require.NoError(t, err)
		assert.Equal(t, StatusPending, updatedVideo.Status)
	})

	t.Run("发布已发布的视频失败", func(t *testing.T) {
		// 创建已发布的视频
		publishedVideo := &Video{
			ID: primitive.NewObjectID(),
			Title: MultilingualString{
				Zh: "已发布视频",
				En: "Published Video",
			},
			Status:     StatusPublished,
			UploaderID: "test-uploader",
		}

		err := repo.Create(ctx, publishedVideo)
		require.NoError(t, err)

		// 尝试再次发布
		err = service.PublishVideo(ctx, publishedVideo.ID.Hex())

		// 验证结果
		assert.Error(t, err)
		// 检查错误是否包含预期的错误信息
		assert.Contains(t, err.Error(), "视频状态不正确")
	})

	t.Run("发布不存在的视频失败", func(t *testing.T) {
		nonExistentID := primitive.NewObjectID().Hex()

		err := service.PublishVideo(ctx, nonExistentID)

		assert.Error(t, err)
		assert.Equal(t, ErrVideoNotFound, err)
	})
}

func TestVideoService_FindVideos(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	service := NewService(repo, suite.Config)
	ctx := context.Background()

	t.Run("按状态过滤查找视频", func(t *testing.T) {
		// 创建不同状态的视频
		draftVideo := &Video{
			ID:         primitive.NewObjectID(),
			Title:      MultilingualString{Zh: "草稿视频", En: "Draft Video"},
			Status:     StatusDraft,
			UploaderID: "test-user",
		}
		publishedVideo := &Video{
			ID:         primitive.NewObjectID(),
			Title:      MultilingualString{Zh: "已发布视频", En: "Published Video"},
			Status:     StatusPublished,
			UploaderID: "test-user",
		}

		err := repo.Create(ctx, draftVideo)
		require.NoError(t, err)
		err = repo.Create(ctx, publishedVideo)
		require.NoError(t, err)

		// 查找已发布的视频
		filter := VideoFilter{
			Status: []string{StatusPublished},
			Page:   1,
			Limit:  10,
		}

		videos, pagination, err := service.FindVideos(ctx, filter)

		require.NoError(t, err)
		require.NotNil(t, pagination)
		assert.Equal(t, int64(1), pagination.TotalItems)
		assert.Len(t, videos, 1)
		assert.Equal(t, StatusPublished, videos[0].Status)
		assert.Equal(t, "已发布视频", videos[0].Title.Zh)
	})

	t.Run("空结果查询", func(t *testing.T) {
		filter := VideoFilter{
			Status: []string{"nonexistent-status"},
			Page:   1,
			Limit:  10,
		}

		videos, pagination, err := service.FindVideos(ctx, filter)

		require.NoError(t, err)
		require.NotNil(t, pagination)
		assert.Equal(t, int64(0), pagination.TotalItems)
		assert.Len(t, videos, 0)
	})
}

func TestVideoService_UpdateStats(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	service := NewService(repo, suite.Config)
	ctx := context.Background()

	t.Run("成功更新视频统计", func(t *testing.T) {
		suite.CleanupDatabase()

		// 先创建一个视频
		video := &Video{
			ID: primitive.NewObjectID(),
			Title: MultilingualString{
				Zh: "统计测试视频",
				En: "Stats Test Video",
			},
			Status:     StatusPublished,
			UploaderID: "test-uploader",
			Stats: VideoStats{
				Views:          100,
				Likes:          10,
				Collections:    5,
				Completions:    80,
				CompletionRate: "80.0%",
			},
		}

		err := repo.Create(ctx, video)
		require.NoError(t, err)

		// 更新统计数据
		views := int64(5)
		likes := int64(2)
		collections := int64(1)
		completions := int64(3)

		req := UpdateStatsRequest{
			Views:       &views,
			Likes:       &likes,
			Collections: &collections,
			Completions: &completions,
		}

		err = service.UpdateStats(ctx, video.ID.Hex(), req)

		// 验证结果
		require.NoError(t, err)
	})

	t.Run("更新不存在视频的统计失败", func(t *testing.T) {
		nonExistentID := primitive.NewObjectID().Hex()

		views := int64(5)
		req := UpdateStatsRequest{
			Views: &views,
		}

		err := service.UpdateStats(ctx, nonExistentID, req)

		assert.Error(t, err)
		assert.Equal(t, ErrVideoNotFound, err)
	})

	t.Run("部分字段更新", func(t *testing.T) {
		suite.CleanupDatabase()

		// 创建测试视频
		video := &Video{
			ID:         primitive.NewObjectID(),
			Title:      MultilingualString{Zh: "部分更新测试", En: "Partial Update Test"},
			Status:     StatusPublished,
			UploaderID: "test-uploader",
			Stats:      VideoStats{Views: 100, Likes: 10},
		}

		err := repo.Create(ctx, video)
		require.NoError(t, err)

		// 只更新views字段
		views := int64(50)
		req := UpdateStatsRequest{
			Views: &views,
			// 其他字段为nil，不应该被更新
		}

		err = service.UpdateStats(ctx, video.ID.Hex(), req)
		require.NoError(t, err)
	})
}

func TestVideoService_DeleteVideo(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	service := NewService(repo, suite.Config)
	ctx := context.Background()

	t.Run("成功删除视频", func(t *testing.T) {
		suite.CleanupDatabase()

		// 先创建一个视频
		video := &Video{
			ID: primitive.NewObjectID(),
			Title: MultilingualString{
				Zh: "待删除视频",
				En: "Video to Delete",
			},
			Status:     StatusDraft,
			UploaderID: "test-uploader",
		}

		err := repo.Create(ctx, video)
		require.NoError(t, err)

		// 删除视频
		err = service.DeleteVideo(ctx, video.ID.Hex())

		// 验证结果
		require.NoError(t, err)

		// 验证视频已被删除
		_, err = repo.FindByID(ctx, video.ID.Hex())
		assert.Error(t, err)
		assert.Equal(t, ErrVideoNotFound, err)
	})

	t.Run("删除不存在的视频失败", func(t *testing.T) {
		nonExistentID := primitive.NewObjectID().Hex()

		err := service.DeleteVideo(ctx, nonExistentID)

		assert.Error(t, err)
		assert.Equal(t, ErrVideoNotFound, err)
	})

	t.Run("删除无效ID格式失败", func(t *testing.T) {
		invalidID := "invalid-id-format"

		err := service.DeleteVideo(ctx, invalidID)

		assert.Error(t, err)
		// 应该是ID格式错误，不是视频未找到错误
		assert.NotEqual(t, ErrVideoNotFound, err)
	})
}
