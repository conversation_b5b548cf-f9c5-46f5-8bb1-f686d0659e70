# 测试环境配置文件

# gomongo数据库配置
[dbs]
verbose = 1  # 日志详细级别

[dbs.realmaster_video]
uri = "*******************************************************************************"

[dbs.data]
uri = "*******************************************************************************"

# 应用程序配置
[server]
port = 8081
host = "127.0.0.1"
draft_dir = "/tmp/rm_video_test_drafts"

# golog日志配置
[golog]
dir = "./test_logs"
level = "debug"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "json"

[media]
server_url = "http://localhost:8081"
storage_dir = "/tmp/rm_video_test_media"

# JWT 认证配置
[auth]
jwtSecret = "test-secret-key-for-testing-only"
devMode = true
devJWTExpireHours = 24

# 事务支持配置
[transaction]
support = false  # 测试环境不使用事务

# goupload 文件上传配置
[userupload]
site = "REALMASTER_TEST"
  [[userupload.types]]
    entryName = "video_draft"
    prefix = "/test/draft/videos"
    tmpPath = "/tmp/goupload_test_temp"
    maxSize = "100MB"  # 测试环境使用较小的限制
    storage = [
      { type = "local", path = "/tmp/rm_video_test_drafts" }
    ]
  [[userupload.types]]
    entryName = "thumbnail_draft"
    prefix = "/test/draft/thumbnails"
    tmpPath = "/tmp/goupload_test_temp"
    maxSize = "10MB"
    storage = [
      { type = "local", path = "/tmp/rm_video_test_drafts" }
    ]
