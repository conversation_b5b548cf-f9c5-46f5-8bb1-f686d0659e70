package main

import (
	"context"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/mongo"

	"realmaster-video-backend/internal/config"
	"realmaster-video-backend/internal/domain/video"
	"realmaster-video-backend/internal/media"
	"realmaster-video-backend/internal/platform/database"
	"realmaster-video-backend/internal/platform/logger" // zap兼容别名
	"realmaster-video-backend/internal/uploader"
)

func main() {
	// 1. 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		panic(fmt.Sprintf("加载配置失败: %v", err))
	}

	// 2. 初始化日志
	if _, err := logger.NewLogger(); err != nil {
		panic(fmt.Sprintf("初始化日志失败: %v", err))
	}
	defer logger.Sync()

	// 3. 初始化数据库连接
	err = database.InitMongoDB()
	if err != nil {
		logger.Log.Fatal("初始化gomongo失败", logger.Error(err))
	}
	// gomongo会自动管理连接，无需手动关闭

	// 4. 初始化 video repository
	videoRepo := video.NewRepository()

	// 5. 初始化 uploader
	fileUploader := uploader.NewHTTPUploader(cfg.Media.ServerURL)

	logger.Log.Info("Video Worker 已启动，开始轮询任务...")

	// 6. 开始无限循环，轮询任务
	// 使用动态轮询间隔：有任务时快速轮询，无任务时延长间隔
	pollInterval := 5 * time.Second
	maxPollInterval := 30 * time.Second

	for {
		// a. 尝试获取并锁定一个待处理的任务
		job, err := videoRepo.FindOneAndUpdateToProcessing(context.Background())
		if err != nil {
			if errors.Is(err, mongo.ErrNoDocuments) {
				// 没有待处理的任务，增加轮询间隔
				if pollInterval < maxPollInterval {
					pollInterval = time.Duration(float64(pollInterval) * 1.5)
					if pollInterval > maxPollInterval {
						pollInterval = maxPollInterval
					}
				}
				logger.Log.Debug("没有待处理的任务，等待下一次轮询...", logger.Duration("interval", pollInterval))
			} else {
				// 发生了其他数据库错误，保持较短间隔重试
				logger.Log.Error("获取任务失败", logger.Error(err))
				pollInterval = 5 * time.Second
			}

			// 等待一段时间再试
			time.Sleep(pollInterval)
			continue
		}

		// 成功获取任务，重置轮询间隔
		pollInterval = 1 * time.Second

		// b. 如果我们到达这里，意味着我们成功获取了一个任务
		logger.Log.Info("成功认领任务", logger.String("videoID", job.ID.Hex()), logger.String("tempVideoPath", job.DraftVideoPath))

		// c. 验证文件并提取元数据
		metadata, err := media.GetMetadata(context.Background(), job.DraftVideoPath)
		if err != nil {
			logger.Log.Error("FFprobe 处理失败", logger.String("videoID", job.ID.Hex()), logger.Error(err))

			// 更新数据库状态为失败
			job.Status = video.StatusProcessingFailed
			job.ProcessingError = err.Error() // 记录错误信息
			// gomongo会自动更新_mt字段

			if updateErr := videoRepo.Update(context.Background(), job); updateErr != nil {
				logger.Log.Error("更新视频状态为 'ProcessingFailed' 失败", logger.String("videoID", job.ID.Hex()), logger.Error(updateErr))
			}

			// 继续下一次循环
			continue
		}

		logger.Log.Info("FFprobe 处理成功",
			logger.String("videoID", job.ID.Hex()),
			logger.String("duration", metadata.Duration.String()),
			logger.Int("width", metadata.Width),
			logger.Int("height", metadata.Height),
		)

		// d. 执行 FFmpeg 转码
		transcodeResult, err := media.Transcode(context.Background(), job.DraftVideoPath)
		if err != nil {
			logger.Log.Error("FFmpeg 转码失败", logger.String("videoID", job.ID.Hex()), logger.Error(err))

			job.Status = video.StatusProcessingFailed
			job.ProcessingError = fmt.Sprintf("FFmpeg aac: %v", err)
			// gomongo会自动更新_mt字段

			if updateErr := videoRepo.Update(context.Background(), job); updateErr != nil {
				logger.Log.Error("更新视频状态为 'ProcessingFailed' 失败", logger.String("videoID", job.ID.Hex()), logger.Error(updateErr))
			}
			continue
		}

		// 将转码结果文件路径记录到日志
		for profile, path := range transcodeResult.VideoOutputs {
			logger.Log.Info("转码输出",
				logger.String("videoID", job.ID.Hex()),
				logger.String("profile", profile),
				logger.String("path", path),
			)
		}

		// e. 执行 Shaka Packager 打包
		packageResult, err := media.Package(context.Background(), transcodeResult)
		if err != nil {
			logger.Log.Error("Shaka Packager 打包失败", logger.String("videoID", job.ID.Hex()), logger.Error(err))

			job.Status = video.StatusProcessingFailed
			job.ProcessingError = fmt.Sprintf("Shaka Packager failed: %v", err)
			// gomongo会自动更新_mt字段

			if updateErr := videoRepo.Update(context.Background(), job); updateErr != nil {
				logger.Log.Error("更新视频状态为 'ProcessingFailed' 失败", logger.String("videoID", job.ID.Hex()), logger.Error(updateErr))
			}
			continue
		}

		logger.Log.Info("Shaka Packager 打包成功",
			logger.String("videoID", job.ID.Hex()),
			logger.String("hls_manifest", packageResult.HLSManifestPath),
		)

		// f. 将打包好的文件上传到文件服务器
		videoID := job.ID.Hex()
		uploadResults, err := fileUploader.UploadDirectory(context.Background(), transcodeResult.PackagingDir, videoID)
		if err != nil {
			logger.Log.Error("上传文件失败", logger.String("videoID", videoID), logger.Error(err))

			job.Status = video.StatusProcessingFailed
			job.ProcessingError = fmt.Sprintf("Upload failed: %v", err)
			// gomongo会自动更新_mt字段

			if updateErr := videoRepo.Update(context.Background(), job); updateErr != nil {
				logger.Log.Error("更新视频状态为 'ProcessingFailed' 失败", logger.String("videoID", videoID), logger.Error(updateErr))
			}
			continue
		}

		for _, r := range uploadResults {
			logger.Log.Info("文件上传成功", logger.String("videoID", videoID), logger.String("relativePath", r.URL))
		}

		// 提前保存需要清理的临时路径
		tempVideoPath := job.DraftVideoPath
		tempThumbPath := job.DraftThumbPath

		var finalThumbRelativePath string
		// g. 上传封面图（如果存在）
		if tempThumbPath != "" {
			thumbUploadResult, err := fileUploader.UploadFile(context.Background(), tempThumbPath, videoID)
			if err != nil {
				// 封面上传失败不是致命错误，只记录日志，视频仍然可以发布
				logger.Log.Error("上传封面图失败", logger.String("videoID", videoID), logger.String("path", tempThumbPath), logger.Error(err))
			} else {
				finalThumbRelativePath = thumbUploadResult.URL // 这里现在是相对路径
				logger.Log.Info("封面图上传成功", logger.String("videoID", videoID), logger.String("relativePath", finalThumbRelativePath))
			}
		}

		// h. 从上传结果中找到HLS主清单文件的相对路径
		// 优先查找主清单文件（以ManifestsBaseName命名的.m3u8文件）
		var hlsPath string
		masterManifestName := transcodeResult.ManifestsBaseName + ".m3u8"

		// 首先查找主清单文件
		for _, r := range uploadResults {
			if r.FileName == masterManifestName {
				hlsPath = r.URL // 这里现在是相对路径
				break
			}
		}

		// 如果没找到主清单文件，则查找任意.m3u8文件作为降级
		if hlsPath == "" {
			for _, r := range uploadResults {
				if strings.HasSuffix(r.FileName, ".m3u8") {
					hlsPath = r.URL
					break
				}
			}
		}
		if hlsPath == "" {
			logger.Log.Error("未能从上传结果中找到HLS清单路径", logger.String("videoID", videoID))
			// 这是致命错误，因为没有播放地址
			job.Status = video.StatusProcessingFailed
			job.ProcessingError = "未能找到HLS清单路径"
			// gomongo会自动更新_mt字段
			if updateErr := videoRepo.Update(context.Background(), job); updateErr != nil {
				logger.Log.Error("更新视频状态为 'ProcessingFailed' 失败", logger.String("videoID", videoID), logger.Error(updateErr))
			}
			continue
		}

		// i. 更新数据库，将视频状态发布
		now := time.Now()
		job.Status = video.StatusPublished
		job.HlsURL = hlsPath
		job.Duration = metadata.DurationSeconds
		job.ManifestsBaseName = transcodeResult.ManifestsBaseName // 保存base name
		job.PublishedAt = &now
		// gomongo会自动更新_mt字段
		job.ProcessingError = "" // 清除之前的错误信息
		job.DraftVideoPath = ""   // 清理临时路径字段
		job.DraftThumbPath = ""   // 清理临时路径字段

		if finalThumbRelativePath != "" {
			job.ThumbURL = finalThumbRelativePath
		} else if tempThumbPath != "" {
			// 如果之前有临时封面但上传失败了，确保最终的URL字段是空的
			job.ThumbURL = ""
		}

		if err := videoRepo.Update(context.Background(), job); err != nil {
			logger.Log.Error("发布视频失败（最终数据库更新失败）", logger.String("videoID", videoID), logger.Error(err))
			// 这是一个严重问题，文件已上传但数据库状态未同步
			// 我们选择继续，不删除文件，以便手动干预
			continue
		}

		logger.Log.Info("视频发布成功", logger.String("videoID", videoID))

		// j. 清理本地临时文件
		if err := os.RemoveAll(transcodeResult.PackagingDir); err != nil {
			logger.Log.Error("清理打包目录失败", logger.String("dir", transcodeResult.PackagingDir), logger.Error(err))
		}

		// 清理临时视频文件并清理空目录
		if err := os.Remove(tempVideoPath); err != nil {
			logger.Log.Error("清理临时视频文件失败", logger.String("path", tempVideoPath), logger.Error(err))
		} else {
			// 清理视频文件所在的空目录
			tempVideoDir := filepath.Dir(tempVideoPath)
			// 使用简单的目录清理逻辑
			if err := os.Remove(tempVideoDir); err == nil {
				logger.Log.Info("清理空目录", logger.String("path", tempVideoDir))
			}
		}

		// 清理临时封面文件并清理空目录
		if tempThumbPath != "" {
			if err := os.Remove(tempThumbPath); err != nil {
				logger.Log.Error("清理临时封面文件失败", logger.String("path", tempThumbPath), logger.Error(err))
			} else {
				// 清理封面文件所在的空目录
				tempThumbDir := filepath.Dir(tempThumbPath)
				// 使用简单的目录清理逻辑
				if err := os.Remove(tempThumbDir); err == nil {
					logger.Log.Info("清理空目录", logger.String("path", tempThumbDir))
				}
			}
		}

		logger.Log.Info("任务处理与清理全部完成", logger.String("videoID", job.ID.Hex()))
	}
}
